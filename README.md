# PageGPT Chrome插件

一个智能的Chrome浏览器插件，通过AI对话的方式帮助用户理解和分析网页内容。

## 🎉 最新版本特性 (v2.1)

### ⚡ SSE流式输出
- **实时响应**: 支持Server-Sent Events流式输出，AI回复实时显示
- **打字效果**: 模拟真人打字的视觉效果，提供自然的对话体验
- **流畅交互**: 无需等待完整响应，即时看到AI的思考过程

### 📝 Markdown格式支持
- **丰富格式**: 完整支持Markdown语法渲染
- **代码高亮**: 集成highlight.js，支持多种编程语言语法高亮
- **表格支持**: 支持表格、列表、引用等复杂格式
- **链接渲染**: 自动渲染链接和格式化文本

### 💬 智能对话交互
- **连续对话**: 支持多轮问答，AI会记住对话历史
- **聊天记录持久化**: 关闭插件后重新打开，聊天记录自动恢复
- **页面理解**: AI能够分析当前页面内容并基于此回答问题
- **智能回复**: 基于页面上下文提供相关和有用的回答
- **按页面分组**: 不同页面的聊天记录分别保存，互不干扰

### ⚙️ 灵活配置
- **多服务支持**: 兼容OpenAI、Azure OpenAI等多种AI服务
- **自定义模型**: 支持选择不同的AI模型(GPT-3.5, GPT-4等)
- **个性化设置**: 可自定义系统提示词来调整AI的行为风格

### 🎨 现代化界面
- **双页面设计**: 分离的设置页面和对话页面
- **美观聊天界面**: 消息气泡、流式动画、滚动优化
- **响应式设计**: 适配不同屏幕尺寸

## 功能特点

- 🤖 **AI驱动**: 使用先进的GPT模型进行智能对话
- ⚡ **流式输出**: SSE实时响应，提供自然的对话体验
- 📝 **Markdown渲染**: 支持丰富的格式化内容展示
- 💾 **聊天记录持久化**: 自动保存和恢复聊天历史
- 🎯 **上下文理解**: 自动分析页面内容，提供相关回答
- 💬 **连续对话**: 支持多轮问答，保持对话上下文
- 🎨 **美观界面**: 现代化的聊天界面设计
- 🔧 **灵活配置**: 支持多种AI服务和自定义设置
- 🔒 **隐私保护**: 本地处理，不上传个人信息

## 安装方法

1. 下载或克隆此项目到本地
2. **创建图标文件（可选）**：
   - 运行 `python create-simple-icons.py` 或 `node generate-icons.js`
   - 或打开 `create-icons.html` 按照说明手动创建
   - 或暂时跳过此步骤（插件会使用默认图标）
3. 打开Chrome浏览器，进入 `chrome://extensions/`
4. 开启"开发者模式"
5. 点击"加载已解压的扩展程序"
6. 选择项目文件夹

## 使用方法

### 首次设置
1. 点击浏览器工具栏中的插件图标
2. 输入你的OpenAI API密钥
3. 可选：自定义提示词来控制AI生成的内容风格

### 分析网页
1. 在任何网页上点击插件图标
2. 点击"分析页面"按钮
3. AI将分析当前页面内容并显示有趣的弹窗

## API密钥获取

1. 访问 [OpenAI官网](https://platform.openai.com/)
2. 注册账号并登录
3. 进入API Keys页面
4. 创建新的API密钥
5. 将密钥复制到插件设置中

## 自定义提示词示例

- **幽默风格**: "请用幽默的方式总结这个网页，并给出一个有趣的评论"
- **学习助手**: "请提取这个页面的关键知识点，并给出学习建议"
- **新闻分析**: "请分析这篇文章的主要观点，并提供不同角度的思考"
- **购物助手**: "请分析这个商品页面，给出购买建议和注意事项"

## 文件结构

```
chrome-assistant/
├── manifest.json          # 插件配置文件
├── popup.html             # 弹窗界面(重新设计)
├── popup.js               # 弹窗逻辑(支持SSE和Markdown)
├── content.js             # 内容脚本
├── content.css            # 弹窗样式
├── background.js          # 后台脚本
├── icons/                 # 插件图标
├── test-page.html         # 功能测试页面
├── chat-test.html         # 对话功能测试页面
├── sse-markdown-test.html # SSE流式输出和Markdown测试页面
├── markdown-test.html     # Markdown渲染测试页面
├── chat-persistence-test.html # 聊天记录持久化测试页面
├── debug-test.html        # 调试测试页面
├── INSTALL.md             # 详细安装指南
└── README.md              # 项目说明
```

## 技术特点

- **Manifest V3**: 使用最新的Chrome扩展API
- **现代JavaScript**: ES6+语法和异步处理
- **响应式设计**: 适配不同屏幕尺寸
- **安全性**: API密钥本地存储，不上传到服务器

## 注意事项

- 需要有效的OpenAI API密钥才能使用
- API调用会产生费用，请注意使用量
- 某些网站可能有内容安全策略限制
- 建议在使用前测试API密钥是否有效

## 故障排除

### 常见问题

1. **插件无法加载 - 图标错误**
   - 错误信息：`Could not load icon 'icons/icon16.png'`
   - 解决方案：运行图标生成脚本或手动创建PNG图标文件
   - 快速解决：图标是可选的，插件功能不受影响

2. **弹窗不显示**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 查看浏览器控制台是否有错误

3. **API调用失败**
   - 验证API密钥格式
   - 检查OpenAI账户余额
   - 确认API使用限制

4. **插件无法加载**
   - 确认所有文件都在正确位置
   - 检查manifest.json语法
   - 重新加载插件

## 开发和贡献

欢迎提交Issue和Pull Request来改进这个项目！

### 开发环境设置
1. 克隆项目
2. 在Chrome中加载插件
3. 修改代码后重新加载插件进行测试

## 许可证

MIT License - 详见LICENSE文件

## 更新日志

### v2.2.0 (当前版本)
- 💾 **聊天记录持久化**: 自动保存和恢复聊天历史，关闭插件后重新打开不丢失
- 📄 **按页面分组**: 不同页面的聊天记录分别保存，互不干扰
- 🗑️ **智能清理**: 自动清理30天前的旧记录，保持存储整洁
- 📊 **聊天统计**: 显示消息数量、时间等统计信息
- ⚠️ **确认对话**: 清空聊天时显示确认提示，防止误操作

### v2.1.0
- 🎉 **SSE流式输出**: 支持Server-Sent Events实时响应
- 📝 **Markdown渲染**: 完整支持Markdown格式和代码高亮
- ⚡ **流畅体验**: 打字光标动画和实时内容更新
- 🔧 **错误处理**: 增强的错误提示和恢复机制
- 📚 **测试页面**: 新增SSE和Markdown功能测试页面

### v2.0.0
- 💬 **对话界面**: 重新设计为聊天式交互
- 🔄 **连续对话**: 支持多轮问答和上下文记忆
- ⚙️ **灵活配置**: 支持多种AI服务和自定义设置
- 🎨 **现代化UI**: 双页面设计和美观的聊天界面

### v1.0.0
- 初始版本发布
- 基本的AI分析和弹窗功能
- 支持自定义提示词
- 现代化UI设计
