<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI助手 - 简化版</title>
  <style>
    * {
      box-sizing: border-box;
    }
    
    body {
      width: 400px;
      height: 600px;
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f5f7fa;
      color: #333;
      overflow: hidden;
    }
    
    .container {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .header h1 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
    
    .nav-tabs {
      display: flex;
      gap: 10px;
    }
    
    .nav-tab {
      padding: 8px 16px;
      background: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 20px;
      color: white;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .nav-tab.active {
      background: rgba(255, 255, 255, 0.3);
      font-weight: 600;
    }
    
    .nav-tab:hover {
      background: rgba(255, 255, 255, 0.25);
    }
    
    .content {
      flex: 1;
      overflow: hidden;
      position: relative;
    }
    
    .page {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      padding: 20px;
      overflow-y: auto;
      transition: transform 0.3s ease;
      background: white;
    }
    
    .page.hidden {
      transform: translateX(100%);
    }
    
    .input-section {
      margin-bottom: 15px;
    }
    
    .input-section label {
      display: block;
      margin-bottom: 5px;
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }
    
    .input-section input, .input-section textarea {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-size: 14px;
      transition: border-color 0.2s;
    }
    
    .input-section input:focus, .input-section textarea:focus {
      outline: none;
      border-color: #667eea;
    }
    
    .input-section textarea {
      height: 80px;
      resize: vertical;
    }
    
    .button-group {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }
    
    button {
      padding: 12px 20px;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .primary-btn {
      background: #667eea;
      color: white;
      flex: 1;
    }
    
    .primary-btn:hover {
      background: #5a6fd8;
    }
    
    .primary-btn:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
    
    .status {
      margin-top: 15px;
      padding: 10px;
      border-radius: 8px;
      font-size: 12px;
      text-align: center;
      display: none;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .status.loading {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }
    
    .chat-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      text-align: center;
      color: #666;
    }
    
    .chat-placeholder h3 {
      margin: 0 0 10px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🤖 PageGPT</h1>
      <div class="nav-tabs">
        <button class="nav-tab active" id="settingsTab">设置</button>
        <button class="nav-tab" id="chatTab">对话</button>
      </div>
    </div>
    
    <div class="content">
      <!-- 设置页面 -->
      <div class="page settings-page" id="settingsPage">
        <div class="input-section">
          <label for="baseUrl">API Base URL:</label>
          <input type="text" id="baseUrl" placeholder="https://api.openai.com/v1/chat/completions">
        </div>
        
        <div class="input-section">
          <label for="modelName">模型名称:</label>
          <input type="text" id="modelName" placeholder="gpt-3.5-turbo">
        </div>
        
        <div class="input-section">
          <label for="apiKey">API密钥:</label>
          <input type="password" id="apiKey" placeholder="输入你的API密钥">
        </div>
        
        <div class="input-section">
          <label for="systemPrompt">系统提示词:</label>
          <textarea id="systemPrompt" placeholder="设置AI的角色和行为方式..."></textarea>
        </div>
        
        <div class="button-group">
          <button class="primary-btn" id="saveSettingsBtn">保存设置</button>
        </div>
        
        <div id="settingsStatus" class="status"></div>
      </div>
      
      <!-- 对话页面 -->
      <div class="page chat-page hidden" id="chatPage">
        <div class="chat-placeholder">
          <div>
            <h3>🎉 设置保存成功！</h3>
            <p>对话功能正在开发中...</p>
            <p>请返回设置页面进行配置</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script src="popup-simple.js"></script>
</body>
</html>
